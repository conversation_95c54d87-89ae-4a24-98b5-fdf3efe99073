<?php

namespace FileSystem;

require_once 'Component.php';

class Folder implements Component {
    /** @var Component[] */
    private array $children = [];

    public function __construct(private string $name) {}

    public function add(Component $component): void {
        $this->children[] = $component;
    }

    public function display(string $indent = ''): void {
        echo $indent . "{$this->name}/\n";
        foreach ($this->children as $child) {
            $child->display($indent . "  ");
        }
    }
}