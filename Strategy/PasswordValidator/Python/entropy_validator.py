import math
from collections import Counter
from password_validator_strategy import PasswordValidatorStrategy


class EntropyValidator(PasswordValidatorStrategy):
    """Validates password based on entropy calculation."""
    
    def is_valid(self, password: str) -> bool:
        """
        Validate that the password has sufficient entropy (>= 3.0).
        
        Args:
            password: The password string to validate
            
        Returns:
            True if entropy >= 3.0, False otherwise
        """
        entropy = self._calculate_entropy(password)
        return entropy >= 3.0
    
    def _calculate_entropy(self, password: str) -> float:
        """
        Calculate the Shannon entropy of a password.
        
        Args:
            password: The password string to analyze
            
        Returns:
            The calculated entropy value
        """
        if not password:
            return 0.0
            
        char_count = Counter(password)
        total_chars = len(password)
        entropy = 0.0
        
        for count in char_count.values():
            probability = count / total_chars
            entropy -= probability * math.log2(probability)
        
        return entropy
