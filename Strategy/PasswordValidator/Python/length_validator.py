from password_validator_strategy import PasswordValidatorStrategy


class LengthValidator(PasswordValidatorStrategy):
    """Validates password based on minimum length requirement."""
    
    def is_valid(self, password: str) -> bool:
        """
        Validate that the password is at least 12 characters long.
        
        Args:
            password: The password string to validate
            
        Returns:
            True if password length >= 12, False otherwise
        """
        return len(password) >= 12
