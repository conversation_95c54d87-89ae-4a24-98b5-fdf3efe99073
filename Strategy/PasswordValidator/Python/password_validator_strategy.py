from abc import ABC, abstractmethod


class PasswordValidatorStrategy(ABC):
    """Abstract base class for password validation strategies."""
    
    @abstractmethod
    def is_valid(self, password: str) -> bool:
        """
        Validate a password according to the specific strategy.
        
        Args:
            password: The password string to validate
            
        Returns:
            True if the password is valid according to this strategy, False otherwise
        """
        pass
