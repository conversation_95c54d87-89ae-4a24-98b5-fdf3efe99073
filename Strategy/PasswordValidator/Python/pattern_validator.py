import re
from password_validator_strategy import PasswordValidatorStrategy


class PatternValidator(PasswordValidatorStrategy):
    """Validates password based on character pattern requirements."""
    
    def is_valid(self, password: str) -> bool:
        """
        Validate that the password contains:
        - At least one uppercase letter
        - At least one lowercase letter  
        - At least one digit
        - At least one special character (non-word character)
        
        Args:
            password: The password string to validate
            
        Returns:
            True if all pattern requirements are met, False otherwise
        """
        return (
            bool(re.search(r'[A-Z]', password)) and
            bool(re.search(r'[a-z]', password)) and
            bool(re.search(r'[0-9]', password)) and
            bool(re.search(r'\W', password))
        )
